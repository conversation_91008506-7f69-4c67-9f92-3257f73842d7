#!/usr/bin/env python3
"""
Test script to verify parallel ChromaDB insertion and timing fixes
"""

import asyncio
import os
import sys
import tempfile
import shutil
from datetime import datetime
from mongoDBInsertion import DocumentProcessor
from chromdb_processor import ChromaDBProcessor
from bson import ObjectId

def create_test_pdf():
    """Create a simple test PDF file for testing"""
    try:
        import fitz  # PyMuPDF
        
        # Create a simple PDF with some text
        doc = fitz.open()
        page = doc.new_page()
        
        # Add some test content
        text = """
        John Doe
        Software Engineer
        
        Experience:
        - Python Developer at Tech Corp (2020-2023)
        - Data Analyst at Data Inc (2018-2020)
        
        Skills:
        - Python, JavaScript, SQL
        - Machine Learning, Data Analysis
        - MongoDB, ChromaDB
        
        Education:
        - Bachelor of Computer Science, Tech University (2018)
        
        Contact:
        Email: <EMAIL>
        Phone: ******-0123
        """
        
        page.insert_text((50, 50), text, fontsize=12)
        
        # Save to temporary file
        temp_dir = tempfile.mkdtemp()
        pdf_path = os.path.join(temp_dir, "test_resume.pdf")
        doc.save(pdf_path)
        doc.close()
        
        return pdf_path, temp_dir
        
    except ImportError:
        print("PyMuPDF not available, creating a dummy file")
        temp_dir = tempfile.mkdtemp()
        pdf_path = os.path.join(temp_dir, "test_resume.pdf")
        
        # Create a dummy PDF-like file
        with open(pdf_path, 'wb') as f:
            f.write(b"%PDF-1.4\n%Test PDF content\n")
        
        return pdf_path, temp_dir

async def test_chromadb_parallel_processing():
    """Test ChromaDB parallel embedding generation"""
    print("🧪 Testing ChromaDB parallel processing...")
    
    try:
        # Initialize ChromaDB processor with parallel settings
        processor = ChromaDBProcessor(
            embedding_workers=4,
            batch_size=5
        )
        
        # Test sample data
        sample_data = {
            "Resume": {
                "PersonalInformation": {
                    "FullName": "Test User",
                    "Email": "<EMAIL>",
                    "ContactNumber": "1234567890"
                },
                "Skills": ["Python", "MongoDB", "ChromaDB", "Machine Learning", "Data Analysis"],
                "WorkExperience": [
                    {
                        "CompanyName": "Tech Corp",
                        "Role": "Software Engineer",
                        "Description/Responsibility": "Developed integration systems and APIs"
                    },
                    {
                        "CompanyName": "Data Inc",
                        "Role": "Data Analyst", 
                        "Description/Responsibility": "Analyzed large datasets and created reports"
                    }
                ],
                "Education": [
                    {
                        "Institution": "Tech University",
                        "Degree": "Bachelor of Computer Science"
                    }
                ]
            }
        }
        
        # Create a fake MongoDB ID for testing
        fake_mongodb_id = ObjectId()
        
        print(f"📊 Processing sample data with MongoDB ID: {fake_mongodb_id}")
        start_time = datetime.now()
        
        # Process the sample data
        success = processor.process_structured_data(
            structured_data=sample_data,
            mongodb_id=fake_mongodb_id,
            original_filename="test_resume.pdf"
        )
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        if success:
            print(f"✅ ChromaDB parallel processing successful in {processing_time:.2f}s")
        else:
            print(f"❌ ChromaDB parallel processing failed")
            
        return success
        
    except Exception as e:
        print(f"❌ ChromaDB test error: {e}")
        return False

async def test_document_processor_timing():
    """Test document processor with timing improvements"""
    print("\n🧪 Testing document processor timing improvements...")
    
    # Create test PDF
    pdf_path, temp_dir = create_test_pdf()
    
    try:
        # Initialize document processor
        processor = DocumentProcessor(
            database_name="test_db",
            collection_name="test_collection",
            n_workers=4,
            enable_chromadb=True
        )
        
        print(f"📄 Processing test PDF: {pdf_path}")
        start_time = datetime.now()
        
        # Process single file
        success = await processor.process_single_file(pdf_path, temp_dir)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        if success:
            print(f"✅ Document processing successful in {processing_time:.2f}s")
        else:
            print(f"❌ Document processing failed")
            
        return success
        
    except Exception as e:
        print(f"❌ Document processor test error: {e}")
        return False
    finally:
        # Cleanup
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

async def test_batch_embedding_generation():
    """Test batch embedding generation performance"""
    print("\n🧪 Testing batch embedding generation...")
    
    try:
        processor = ChromaDBProcessor(embedding_workers=4)
        
        # Test texts
        test_texts = [
            "Python programming language",
            "Machine Learning algorithms", 
            "Data analysis and visualization",
            "MongoDB database management",
            "ChromaDB vector database",
            "Software engineering principles",
            "API development and integration",
            "Cloud computing platforms",
            "Artificial intelligence research",
            "Web application development"
        ]
        
        print(f"🔄 Generating embeddings for {len(test_texts)} texts...")
        start_time = datetime.now()
        
        # Generate embeddings in batch
        results = processor.get_embeddings_batch(test_texts)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        successful_embeddings = sum(1 for _, embedding in results if embedding is not None)
        
        print(f"✅ Generated {successful_embeddings}/{len(test_texts)} embeddings in {processing_time:.2f}s")
        print(f"📈 Throughput: {successful_embeddings/processing_time:.2f} embeddings/second")
        
        return successful_embeddings > 0
        
    except Exception as e:
        print(f"❌ Batch embedding test error: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting parallel processing improvements tests...")
    print("=" * 60)
    
    # Test results
    results = []
    
    # Test 1: ChromaDB parallel processing
    result1 = await test_chromadb_parallel_processing()
    results.append(("ChromaDB Parallel Processing", result1))
    
    # Test 2: Batch embedding generation
    result2 = await test_batch_embedding_generation()
    results.append(("Batch Embedding Generation", result2))
    
    # Test 3: Document processor timing (commented out to avoid AWS costs)
    # result3 = await test_document_processor_timing()
    # results.append(("Document Processor Timing", result3))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success in results if success)
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Parallel improvements are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test execution error: {e}")
        sys.exit(1)
