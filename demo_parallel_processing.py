#!/usr/bin/env python3
"""
Demonstration script showing how to use the improved parallel processing features
"""

import asyncio
import os
from mongoDBInsertion import process_documents_from_folder

async def demo_high_performance_processing():
    """
    Demonstrate high-performance processing with parallel ChromaDB insertion
    and improved AWS Textract timing.
    """
    
    print("🚀 Parallel Processing Demo")
    print("=" * 50)
    
    # Example folder path - replace with your actual folder
    folder_path = r"\\************\user_data\PAVAN\Desktop\Gemini\resumes"
    
    # Check if folder exists
    if not os.path.exists(folder_path):
        print(f"⚠️  Folder not found: {folder_path}")
        print("Please update the folder_path variable with your actual resume folder")
        return
    
    print(f"📁 Processing folder: {folder_path}")
    print(f"🔧 Configuration:")
    print(f"   - Main workers: 12 (parallel file processing)")
    print(f"   - ChromaDB embedding workers: 6 (auto-calculated)")
    print(f"   - ChromaDB batch size: 12 (auto-calculated)")
    print(f"   - AWS Textract: Enhanced timing with retry logic")
    print()
    
    try:
        # Process documents with improved parallel processing
        results = await process_documents_from_folder(
            folder_path=folder_path,
            database_name="dbProductionV2",
            collection_name="collectionResumeV2_chroma",
            n_workers=12,  # Main parallel workers
            enable_chromadb=True,
            chroma_host="localhost",
            chroma_port=8001
        )
        
        # Display results
        print("\n" + "=" * 50)
        print("📊 PROCESSING RESULTS")
        print("=" * 50)
        
        print(f"📄 Total files found: {results.get('total_files', 0)}")
        print(f"✅ Successfully processed: {results.get('processed_successfully', 0)}")
        print(f"❌ Failed: {results.get('failed_files', 0)}")
        print(f"🔄 Duplicates skipped: {results.get('duplicate_files', 0)}")
        print(f"⏱️  Processing time: {results.get('processing_time', 'N/A')}")
        print(f"👥 Workers used: {results.get('n_workers', 'N/A')}")
        
        # Calculate throughput
        if results.get('processed_successfully', 0) > 0:
            processing_time_str = results.get('processing_time', '0:00:00')
            try:
                # Parse time string (format: H:MM:SS.microseconds)
                time_parts = processing_time_str.split(':')
                if len(time_parts) >= 3:
                    hours = int(time_parts[0])
                    minutes = int(time_parts[1])
                    seconds = float(time_parts[2])
                    total_seconds = hours * 3600 + minutes * 60 + seconds
                    
                    if total_seconds > 0:
                        throughput = results['processed_successfully'] / total_seconds
                        print(f"🚀 Throughput: {throughput:.2f} files/second")
            except:
                pass
        
        # Show failed files if any
        if results.get('failed_files_list'):
            print(f"\n❌ Failed files:")
            for file in results['failed_files_list']:
                print(f"   - {file}")
        
        # Show duplicate files if any
        if results.get('duplicate_files_list'):
            print(f"\n🔄 Duplicate files (skipped):")
            for file in results['duplicate_files_list'][:5]:  # Show first 5
                print(f"   - {file}")
            if len(results['duplicate_files_list']) > 5:
                print(f"   ... and {len(results['duplicate_files_list']) - 5} more")
        
        print("\n🎉 Processing completed!")
        
        # Performance tips
        print("\n💡 PERFORMANCE TIPS:")
        print("   - For large datasets (200+ files): Use n_workers=12-20")
        print("   - For small datasets (<50 files): Use n_workers=4-6")
        print("   - Monitor system resources (CPU, memory) during processing")
        print("   - ChromaDB workers and batch size are auto-optimized")
        print("   - AWS Textract now has improved timing and retry logic")
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        print("\n🔧 TROUBLESHOOTING:")
        print("   1. Check if ChromaDB server is running (localhost:8001)")
        print("   2. Verify MongoDB connection")
        print("   3. Check AWS credentials for Textract")
        print("   4. Ensure sufficient system resources")

async def demo_custom_configuration():
    """
    Demonstrate custom configuration options
    """
    
    print("\n" + "=" * 50)
    print("🔧 CUSTOM CONFIGURATION DEMO")
    print("=" * 50)
    
    from mongoDBInsertion import DocumentProcessor
    
    # Example 1: Memory-optimized configuration
    print("📝 Example 1: Memory-optimized configuration")
    processor1 = DocumentProcessor(
        database_name="test_db",
        collection_name="test_collection",
        n_workers=4,  # Lower worker count
        enable_chromadb=True
    )
    print(f"   - Main workers: 4")
    print(f"   - ChromaDB embedding workers: 2 (auto-calculated)")
    print(f"   - ChromaDB batch size: 5 (auto-calculated)")
    
    # Example 2: High-performance configuration
    print("\n📝 Example 2: High-performance configuration")
    processor2 = DocumentProcessor(
        database_name="production_db",
        collection_name="production_collection",
        n_workers=16,  # Higher worker count
        enable_chromadb=True
    )
    print(f"   - Main workers: 16")
    print(f"   - ChromaDB embedding workers: 8 (auto-calculated, capped)")
    print(f"   - ChromaDB batch size: 16 (auto-calculated)")
    
    # Example 3: Custom ChromaDB configuration
    print("\n📝 Example 3: Custom ChromaDB configuration")
    from chromdb_processor import ChromaDBProcessor
    
    custom_chromadb = ChromaDBProcessor(
        chroma_host="localhost",
        chroma_port=8001,
        embedding_workers=8,  # Custom worker count
        batch_size=20,        # Custom batch size
        collection_name="custom_resumes"
    )
    print(f"   - ChromaDB embedding workers: 8 (custom)")
    print(f"   - ChromaDB batch size: 20 (custom)")
    print(f"   - Collection name: custom_resumes")

def main():
    """
    Main demonstration function
    """
    
    print("🎯 PARALLEL PROCESSING IMPROVEMENTS DEMO")
    print("This demo shows the new parallel ChromaDB insertion")
    print("and improved AWS Textract timing features.")
    print()
    
    # Show configuration examples
    asyncio.run(demo_custom_configuration())
    
    # Ask user if they want to run actual processing
    print("\n" + "=" * 50)
    response = input("Do you want to run actual document processing? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        asyncio.run(demo_high_performance_processing())
    else:
        print("👍 Demo completed. Update folder_path in the script to process your documents.")
        print("\n🚀 Key improvements implemented:")
        print("   ✅ Parallel ChromaDB embedding generation")
        print("   ✅ Batch ChromaDB insertions")
        print("   ✅ Improved AWS Textract timing with retry logic")
        print("   ✅ Thread-safe operations")
        print("   ✅ Auto-optimized worker configuration")
        print("   ✅ Enhanced error handling and logging")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Demo error: {e}")
