"""
MongoDB Document Insertion Tool

This module processes various document formats (PDF, DOC, DOCX, images),
converts them to PDF, extracts text using AWS Textract and OpenAI,
and stores the results in MongoDB with GridFS for file storage.

Required additional dependencies (install via pip):
- PyMuPDF (fitz): pip install PyMuPDF
- python-docx: pip install python-docx
- pywin32: pip install pywin32 (Windows only, for DOC conversion)
- Pillow: pip install Pillow (usually already installed)

Usage:
    # Process all files in a folder
    python mongoDBInsertion.py /path/to/documents

    # Process with custom database settings
    python mongoDBInsertion.py /path/to/documents --database my-db --collection my-collection

    # List processed documents
    python mongoDBInsertion.py --list
"""

import os
import json
import asyncio
import hashlib
import logging
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
import shutil
import tempfile
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from queue import Queue

# Document conversion libraries
try:
    from PIL import Image
except ImportError:
    print("Warning: PIL (Pillow) not found. Image conversion will not work.")
    Image = None

try:
    import fitz  # PyMuPDF for PDF operations
except ImportError:
    print("Warning: PyMuPDF (fitz) not found. Advanced PDF operations may not work.")
    fitz = None

try:
    from docx import Document
except ImportError:
    print("Warning: python-docx not found. DOCX conversion will not work.")
    Document = None

try:
    import win32com.client  # For .doc files on Windows
except ImportError:
    print("Warning: pywin32 not found. DOC conversion on Windows will not work.")
    win32com = None

# Existing modules from the project
from openai import OpenAI
from utils import MloadConfig
from AWS_Async import extractByAwsTextract
from helperMongoDb import MongoDBClient
import gridfs
from bson import ObjectId
from chromdb_processor import ChromaDBProcessor

# Constants
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
SUPPORTED_DOC_FORMATS = ['.doc', '.docx']
PDF_FORMAT = '.pdf'
TEXT_CONVERSION_SUFFIX = "_Combined"
GPT_RESPONSE_SUFFIX = "_GPT_Response"

class DocumentProcessor:
    """
    A comprehensive document processor that handles various file formats,
    converts them to PDF, extracts text using AWS Textract and OpenAI,
    and stores the results in MongoDB.
    """
    
    def __init__(self,
                 database_name: str = "dbProductionV2",
                 collection_name: str = "collectionResumeV2_chroma",
                 vendor_name: str = "Resume_Schema",
                 n_workers: int = 12,
                 enable_chromadb: bool = True,
                 chroma_host: str = "localhost",
                 chroma_port: int = 8001):
        """
        Initialize the DocumentProcessor.

        Args:
            database_name: MongoDB database name
            collection_name: MongoDB collection name
            vendor_name: Vendor name for processing configuration
            n_workers: Number of parallel workers for processing
            enable_chromadb: Whether to enable ChromaDB integration
            chroma_host: ChromaDB server host
            chroma_port: ChromaDB server port
        """
        self.database_name = database_name
        self.collection_name = collection_name
        self.vendor_name = vendor_name
        self.n_workers = n_workers
        self.enable_chromadb = enable_chromadb

        # Setup logging first
        self.setup_logging()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Load configuration
        self.config = MloadConfig()

        # Initialize OpenAI client
        self.openai_client = OpenAI(
            api_key="************************************************************************************",
            base_url="https://api.x.ai/v1",
        )

        # Initialize ChromaDB processor if enabled
        if self.enable_chromadb:
            try:
                # Calculate optimal embedding workers based on main workers
                embedding_workers = max(2, min(self.n_workers // 2, 8))  # 2-8 workers for embeddings
                batch_size = max(5, min(self.n_workers, 20))  # 5-20 batch size

                self.chromadb_processor = ChromaDBProcessor(
                    chroma_host=chroma_host,
                    chroma_port=chroma_port,
                    embedding_workers=embedding_workers,
                    batch_size=batch_size,
                    # openai_api_key="************************************************************************************",
                    # openai_base_url="https://api.x.ai/v1"
                )
                self.logger.info(f"ChromaDB processor initialized with {embedding_workers} embedding workers and batch size {batch_size}")
            except Exception as e:
                self.logger.error(f"Failed to initialize ChromaDB processor: {e}")
                self.enable_chromadb = False
                self.chromadb_processor = None
        else:
            self.chromadb_processor = None

        # Initialize MongoDB client with custom database
        self.mongo_client = MongoDBClient(db_name=database_name)

        # Initialize GridFS for file storage
        self.fs = gridfs.GridFS(self.mongo_client.db)

        # Load system prompt and response format
        self.load_ai_configurations()

        # Thread-safe locks for shared resources
        self._mongo_lock = threading.Lock()
        self._gridfs_lock = threading.Lock()
        self._openai_lock = threading.Lock()  # Rate limiting for OpenAI API
        self._com_lock = threading.Lock()  # For Windows COM operations (Word automation)
    
    def setup_logging(self):
        """Setup logging configuration."""
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(log_dir, 'document_processor.log')),
                logging.StreamHandler()
            ]
        )
    
    def load_ai_configurations(self):
        """Load system prompt and response format for AI processing."""
        try:
            # Load system prompt
            system_prompt_path = self.config.get("systemPromptFilePath")
            with open(system_prompt_path, 'r', encoding='utf-8') as file:
                self.system_prompt = file.read()
            
            # Load response format
            response_format_path = self.config.get("responseFormatFilePath")
            with open(response_format_path, 'r', encoding='utf-8') as file:
                response_formats = json.load(file)
                self.response_format = response_formats.get(self.vendor_name, {})
            
            self.logger.info("AI configurations loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Error loading AI configurations: {e}")
            raise
    
    def calculate_checksum(self, file_path: str) -> str:
        """Calculate SHA-256 checksum of a file."""
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except Exception as e:
            self.logger.error(f"Error calculating checksum for {file_path}: {e}")
            raise
    
    def is_duplicate(self, checksum: str) -> bool:
        """Check if a document with the same checksum already exists."""
        try:
            collection = self.mongo_client.db[self.collection_name]
            existing = collection.find_one({"checksum": checksum})
            return existing is not None
        except Exception as e:
            self.logger.error(f"Error checking for duplicates: {e}")
            return False
    
    def convert_image_to_pdf(self, image_path: str, output_path: str) -> bool:
        """Convert image file to PDF."""
        if Image is None:
            self.logger.error("PIL (Pillow) not available. Cannot convert images to PDF.")
            return False

        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Save as PDF
                img.save(output_path, "PDF", resolution=100.0)
                self.logger.info(f"Converted image {image_path} to PDF: {output_path}")
                return True

        except Exception as e:
            self.logger.error(f"Error converting image {image_path} to PDF: {e}")
            return False

    def convert_docx_to_pdf(self, docx_path: str, output_path: str) -> bool:
        """Convert DOCX file to PDF using COM automation on Windows."""
        try:
            # For Windows, use COM automation
            if os.name == 'nt':
                return self.convert_doc_to_pdf_windows(docx_path, output_path)
            else:
                # For other platforms, you might need LibreOffice or other tools
                self.logger.warning("DOCX to PDF conversion not fully supported on non-Windows platforms")
                return False

        except Exception as e:
            self.logger.error(f"Error converting DOCX {docx_path} to PDF: {e}")
            return False

    def convert_doc_to_pdf_windows(self, doc_path: str, output_path: str) -> bool:
        """Convert DOC/DOCX to PDF using Windows COM automation (thread-safe)."""
        if win32com is None:
            self.logger.error("pywin32 not available. Cannot convert DOC/DOCX files on Windows.")
            return False

        # Use lock to prevent concurrent COM operations
        with self._com_lock:
            try:
                import pythoncom

                # Initialize COM for this thread
                pythoncom.CoInitialize()

                try:
                    # Use Microsoft Word COM automation
                    word = win32com.client.Dispatch("Word.Application")
                    word.Visible = False

                    # Open document
                    doc = word.Documents.Open(os.path.abspath(doc_path))

                    # Save as PDF (format 17 is PDF)
                    doc.SaveAs(os.path.abspath(output_path), FileFormat=17)

                    # Close document and Word
                    doc.Close()
                    word.Quit()

                    self.logger.info(f"Converted {doc_path} to PDF: {output_path}")
                    return True

                finally:
                    # Always uninitialize COM
                    pythoncom.CoUninitialize()

            except Exception as e:
                self.logger.error(f"Error converting {doc_path} to PDF using COM: {e}")
                return False
    
    def convert_to_pdf(self, file_path: str, output_dir: str) -> Optional[str]:
        """
        Convert various file formats to PDF.
        
        Args:
            file_path: Path to the input file
            output_dir: Directory to save the converted PDF
            
        Returns:
            Path to the converted PDF file, or None if conversion failed
        """
        file_ext = Path(file_path).suffix.lower()
        file_name = Path(file_path).stem
        output_path = os.path.join(output_dir, f"{file_name}.pdf")
        
        # If already PDF, just copy it
        if file_ext == PDF_FORMAT:
            shutil.copy2(file_path, output_path)
            return output_path
        
        # Convert based on file type
        if file_ext in SUPPORTED_IMAGE_FORMATS:
            if self.convert_image_to_pdf(file_path, output_path):
                return output_path
        elif file_ext in SUPPORTED_DOC_FORMATS:
            if self.convert_docx_to_pdf(file_path, output_path):
                return output_path
        else:
            self.logger.warning(f"Unsupported file format: {file_ext}")
            return None

        return None

    async def extract_text_from_pdf(self, pdf_path: str) -> Optional[str]:
        """Extract text from PDF using AWS Textract with proper waiting for file completion."""
        try:
            # Match AWS Textract's exact folder structure logic (now with trailing space fix)
            # AWS Textract uses: os.path.splitext(os.path.basename(local_pdf_path))[0].rstrip()
            pdf_name = os.path.splitext(os.path.basename(pdf_path))[0].rstrip()
            file_dir = os.path.dirname(os.path.abspath(pdf_path))
            pdf_folder = os.path.join(file_dir, pdf_name)

            self.logger.info(f"PDF name (sanitized): '{pdf_name}' (length: {len(pdf_name)})")
            self.logger.info(f"AWS Textract will create folder: {pdf_folder}")

            # Don't create the folder ourselves - let AWS Textract create it
            # os.makedirs(pdf_folder, exist_ok=True)

            # AWS Textract creates these specific files (now with sanitized names):
            possible_text_files = [
                os.path.join(pdf_folder, f"{pdf_name}_ExtractedText.txt"),  # Primary AWS Textract output (CSV format)
                os.path.join(pdf_folder, f"{pdf_name}_strUserPrompt.txt"),  # Combined text file from AWS Textract
                os.path.join(pdf_folder, f"{pdf_name}_Text.json"),  # JSON format from AWS Textract
                os.path.join(pdf_folder, f"{pdf_name}{TEXT_CONVERSION_SUFFIX}.txt"),  # Legacy format
                os.path.join(pdf_folder, f"{pdf_name}_Combined.txt")  # Another possible format
            ]

            self.logger.info(f"Will check {len(possible_text_files)} possible file locations")

            # Extract text if not already exists
            text_file_exists = any(os.path.exists(path) for path in possible_text_files)
            if not text_file_exists:
                self.logger.info(f"Starting AWS Textract extraction for: {pdf_path}")
                self.logger.info(f"Expected output folder: {pdf_folder}")

                await extractByAwsTextract(pdf_path)
                self.logger.info(f"AWS Textract extraction completed for: {pdf_path}")

                # Wait for the output folder to be created first
                folder_wait_time = 30  # Wait up to 30 seconds for folder creation
                folder_wait_interval = 1
                folder_total_waited = 0

                while folder_total_waited < folder_wait_time and not os.path.exists(pdf_folder):
                    await asyncio.sleep(folder_wait_interval)
                    folder_total_waited += folder_wait_interval
                    self.logger.debug(f"Waiting for folder creation... ({folder_total_waited}s elapsed)")

                if os.path.exists(pdf_folder):
                    self.logger.info(f"AWS Textract created folder: {pdf_folder}")
                else:
                    self.logger.warning(f"Output folder not created after {folder_wait_time}s: {pdf_folder}")

                # Wait for text files to be created with exponential backoff
                max_wait_time = 120  # Maximum wait time in seconds
                wait_interval = 1    # Initial wait interval
                total_waited = 0

                while total_waited < max_wait_time:
                    # First check predefined paths
                    text_file_exists = any(os.path.exists(path) for path in possible_text_files)

                    # Dynamically scan the folder for any text files that might have been created
                    if not text_file_exists and os.path.exists(pdf_folder):
                        try:
                            folder_files = os.listdir(pdf_folder)
                            # Look for any text or JSON files with content
                            text_files = []
                            for f in folder_files:
                                if f.endswith(('.txt', '.json')):
                                    full_path = os.path.join(pdf_folder, f)
                                    try:
                                        if os.path.getsize(full_path) > 0:
                                            text_files.append(f)
                                            # Add to possible files if not already there
                                            if full_path not in possible_text_files:
                                                possible_text_files.append(full_path)
                                    except OSError:
                                        continue  # File might still be being written

                            if text_files:
                                self.logger.info(f"Found text files in folder: {text_files}")
                                text_file_exists = True
                        except Exception as e:
                            self.logger.debug(f"Error scanning folder: {e}")

                    if text_file_exists:
                        self.logger.info(f"Text files detected after {total_waited}s wait")
                        break

                    # Wait with exponential backoff
                    await asyncio.sleep(wait_interval)
                    total_waited += wait_interval
                    wait_interval = min(wait_interval * 1.5, 10)  # Cap at 10 seconds

                    self.logger.debug(f"Waiting for text extraction files... ({total_waited}s elapsed)")

                if total_waited >= max_wait_time:
                    self.logger.error(f"Timeout waiting for text extraction files for {pdf_path}")

            # Try to read from any of the possible text files with retry logic
            max_read_attempts = 5
            for attempt in range(max_read_attempts):
                # Refresh the list of possible files on each attempt
                if attempt > 0:
                    # Use dynamic file finding for subsequent attempts
                    dynamic_files = self._find_textract_output_files(pdf_folder, pdf_name)
                    # Combine with original list, prioritizing dynamic findings
                    all_possible_files = dynamic_files + [f for f in possible_text_files if f not in dynamic_files]
                else:
                    all_possible_files = possible_text_files

                for text_file_path in all_possible_files:
                    if os.path.exists(text_file_path):
                        try:
                            # Wait a bit to ensure file is fully written
                            if attempt == 0:
                                await asyncio.sleep(0.5)

                            # Handle different file formats
                            if text_file_path.endswith('.json'):
                                # Handle JSON format from AWS Textract
                                with open(text_file_path, 'r', encoding='utf-8') as file:
                                    import json
                                    json_data = json.load(file)
                                    # Extract text content from JSON structure
                                    if isinstance(json_data, dict):
                                        content = self._extract_text_from_json(json_data)
                                    else:
                                        content = str(json_data)
                            else:
                                # Handle text files
                                with open(text_file_path, 'r', encoding='utf-8') as file:
                                    content = file.read().strip()

                            if content:  # Make sure we have actual content
                                self.logger.info(f"Successfully read text from: {text_file_path}")
                                return content
                            else:
                                self.logger.warning(f"Text file exists but is empty: {text_file_path}")
                        except Exception as e:
                            self.logger.warning(f"Failed to read {text_file_path} (attempt {attempt + 1}): {e}")
                            continue

                # If no content found, wait before retrying
                if attempt < max_read_attempts - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff

            # If no text file found, provide comprehensive debugging information
            self.logger.error(f"Text extraction failed for {pdf_path}")
            self.logger.error(f"PDF name (sanitized): '{pdf_name}'")
            self.logger.error(f"Expected folder: {pdf_folder}")
            self.logger.error(f"Folder exists: {os.path.exists(pdf_folder)}")

            # Check the parent directory
            parent_dir = os.path.dirname(pdf_path)
            self.logger.error(f"Parent directory: {parent_dir}")
            if os.path.exists(parent_dir):
                parent_contents = os.listdir(parent_dir)
                self.logger.error(f"Parent directory contents: {parent_contents}")

                # Look for any folders that might have been created
                for item in parent_contents:
                    item_path = os.path.join(parent_dir, item)
                    if os.path.isdir(item_path):
                        self.logger.error(f"Found directory: '{item}' (length: {len(item)})")
                        try:
                            dir_contents = os.listdir(item_path)
                            self.logger.error(f"  Contents: {dir_contents}")
                        except Exception as e:
                            self.logger.error(f"  Error reading contents: {e}")

            # Check the expected folder
            if os.path.exists(pdf_folder):
                try:
                    files_in_folder = os.listdir(pdf_folder)
                    self.logger.error(f"Folder contents: {files_in_folder}")

                    # Check file sizes
                    for file_name in files_in_folder:
                        file_path = os.path.join(pdf_folder, file_name)
                        if os.path.isfile(file_path):
                            file_size = os.path.getsize(file_path)
                            self.logger.error(f"  - {file_name}: {file_size} bytes")
                except Exception as e:
                    self.logger.error(f"Error reading folder: {e}")

            return None

        except Exception as e:
            self.logger.error(f"Error extracting text from {pdf_path}: {e}")
            return None

    def _extract_text_from_json(self, json_data: dict) -> str:
        """Extract text content from AWS Textract JSON format."""
        try:
            text_parts = []

            # Handle different JSON structures that AWS Textract might return
            if isinstance(json_data, dict):
                # Try to extract text from various possible structures
                for _, page_data in json_data.items():
                    if isinstance(page_data, list):
                        for item in page_data:
                            if isinstance(item, list) and len(item) > 0:
                                # Format: [text, x1, y1, x2, y2]
                                text_parts.append(str(item[0]))
                            elif isinstance(item, str):
                                text_parts.append(item)

            return '\n'.join(text_parts) if text_parts else ""

        except Exception as e:
            self.logger.warning(f"Error extracting text from JSON: {e}")
            return str(json_data)  # Fallback to string representation

    def _find_textract_output_files(self, pdf_folder: str, pdf_name: str = None) -> List[str]:
        """
        Dynamically find all text files created by AWS Textract in the output folder.

        Args:
            pdf_folder: The folder where AWS Textract outputs are stored
            pdf_name: The base name of the PDF file

        Returns:
            List of full paths to potential text files
        """
        found_files = []

        if not os.path.exists(pdf_folder):
            return found_files

        try:
            # Get all files in the folder
            all_files = os.listdir(pdf_folder)

            # Look for text files that might contain extracted content
            for file_name in all_files:
                file_path = os.path.join(pdf_folder, file_name)

                # Check if it's a text or JSON file with content
                if (file_name.endswith(('.txt', '.json')) and
                    os.path.isfile(file_path) and
                    os.path.getsize(file_path) > 0):

                    # Prioritize files that match expected patterns
                    if any(pattern in file_name for pattern in [
                        '_ExtractedText', '_Text', '_Combined', '_strUserPrompt'
                    ]):
                        found_files.insert(0, file_path)  # Add to beginning (higher priority)
                    else:
                        found_files.append(file_path)  # Add to end (lower priority)

            self.logger.debug(f"Found {len(found_files)} potential text files in {pdf_folder}")

        except Exception as e:
            self.logger.warning(f"Error scanning folder {pdf_folder}: {e}")

        return found_files
    
    def process_with_openai(self, text_content: str) -> Optional[Dict[str, Any]]:
        """Process extracted text using OpenAI to structure the data (thread-safe)."""
        with self._openai_lock:  # Rate limiting for OpenAI API
            try:
                self.logger.info("Starting OpenAI processing...")

                response = self.openai_client.beta.chat.completions.parse(
                    model="grok-3-mini",
                    messages=[
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": text_content}
                    ],
                    response_format=self.response_format,
                    reasoning_effort="high"
                    # temperature=0,
                    # max_completion_tokens=16384,
                    # seed=33
                )

                self.logger.info("OpenAI processing completed")

                # Parse the response
                structured_data = json.loads(response.choices[0].message.content)
                return structured_data

            except Exception as e:
                self.logger.error(f"Error processing with OpenAI: {e}")
                return None
    
    def store_pdf_in_gridfs(self, pdf_path: str, filename: str) -> Optional[ObjectId]:
        """Store PDF file in MongoDB GridFS (thread-safe)."""
        with self._gridfs_lock:
            try:
                with open(pdf_path, 'rb') as pdf_file:
                    file_id = self.fs.put(
                        pdf_file,
                        filename=filename,
                        content_type='application/pdf',
                        upload_date=datetime.now()
                    )
                    self.logger.info(f"Stored PDF {filename} in GridFS with ID: {file_id}")
                    return file_id

            except Exception as e:
                self.logger.error(f"Error storing PDF in GridFS: {e}")
                return None

    def insert_to_mongodb(self, structured_data: Dict[str, Any], checksum: str,
                         pdf_file_id: Optional[ObjectId], original_filename: str) -> Optional[ObjectId]:
        """Insert structured data and PDF reference to MongoDB (thread-safe)."""
        with self._mongo_lock:
            try:
                collection = self.mongo_client.db[self.collection_name]

                # Prepare the document
                document = {
                    **structured_data,
                    "checksum": checksum,
                    "timestamp": datetime.now(),
                    "original_filename": original_filename,
                    "pdf_file_id": pdf_file_id,
                    "vendor_name": self.vendor_name,
                    "processed_by": "mongoDBInsertion.py"
                }

                # Insert the document
                result = collection.insert_one(document)
                self.logger.info(f"Inserted document with ID: {result.inserted_id}")
                return result.inserted_id

            except Exception as e:
                self.logger.error(f"Error inserting to MongoDB: {e}")
                return None

    def is_duplicate_thread_safe(self, checksum: str) -> bool:
        """Check if a document with the same checksum already exists (thread-safe)."""
        with self._mongo_lock:
            try:
                collection = self.mongo_client.db[self.collection_name]
                existing = collection.find_one({"checksum": checksum})
                return existing is not None
            except Exception as e:
                self.logger.error(f"Error checking for duplicates: {e}")
                return False

    def delete_document_by_id(self, mongodb_id: ObjectId) -> bool:
        """
        Delete a document from both MongoDB and ChromaDB by MongoDB ID.

        Args:
            mongodb_id: MongoDB ObjectId to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete from MongoDB
            with self._mongo_lock:
                collection = self.mongo_client.db[self.collection_name]
                mongo_result = collection.delete_one({"_id": mongodb_id})

                if mongo_result.deleted_count > 0:
                    self.logger.info(f"Deleted document from MongoDB: {mongodb_id}")
                    mongo_success = True
                else:
                    self.logger.warning(f"No document found in MongoDB with ID: {mongodb_id}")
                    mongo_success = False

            # Delete from ChromaDB if enabled
            chromadb_success = True
            if self.enable_chromadb and self.chromadb_processor:
                try:
                    chromadb_success = self.chromadb_processor.delete_by_mongodb_id(mongodb_id)
                except Exception as e:
                    self.logger.error(f"Error deleting from ChromaDB: {e}")
                    chromadb_success = False

            return mongo_success and chromadb_success

        except Exception as e:
            self.logger.error(f"Error deleting document {mongodb_id}: {e}")
            return False

    async def process_single_file(self, file_path: str, temp_dir: str) -> bool:
        """
        Process a single file: convert to PDF, extract text, process with AI, and store in MongoDB.

        Args:
            file_path: Path to the file to process
            temp_dir: Temporary directory for intermediate files

        Returns:
            True if processing was successful, False otherwise
        """
        try:
            original_filename = os.path.basename(file_path)
            self.logger.info(f"Processing file: {original_filename}")

            # Calculate checksum of original file
            checksum = self.calculate_checksum(file_path)

            # Check for duplicates (thread-safe)
            if self.is_duplicate_thread_safe(checksum):
                self.logger.info(f"Duplicate file detected, skipping: {original_filename}")
                return True

            # Convert to PDF if necessary
            pdf_path = self.convert_to_pdf(file_path, temp_dir)
            if not pdf_path:
                self.logger.error(f"Failed to convert {original_filename} to PDF")
                return False

            # Extract text using AWS Textract
            extracted_text = await self.extract_text_from_pdf(pdf_path)
            if not extracted_text:
                self.logger.error(f"Failed to extract text from {original_filename}")
                return False

            # Process with OpenAI (thread-safe with rate limiting)
            structured_data = self.process_with_openai(extracted_text)
            if not structured_data:
                self.logger.error(f"Failed to process {original_filename} with OpenAI")
                return False

            # Store PDF in GridFS (thread-safe)
            pdf_file_id = self.store_pdf_in_gridfs(pdf_path, original_filename)

            # Insert to MongoDB (thread-safe) - now returns ObjectId
            mongodb_id = self.insert_to_mongodb(structured_data, checksum, pdf_file_id, original_filename)

            if mongodb_id:
                self.logger.info(f"Successfully stored in MongoDB with ID: {mongodb_id}")

                # Add MongoDB ID to structured_data for ChromaDB
                structured_data_with_id = {
                    **structured_data,
                    "mongodb_id": str(mongodb_id)
                }

                # Process with ChromaDB if enabled
                if self.enable_chromadb and self.chromadb_processor:
                    try:
                        chromadb_success = self.chromadb_processor.process_structured_data(
                            structured_data_with_id, mongodb_id, original_filename
                        )
                        if chromadb_success:
                            self.logger.info(f"Successfully stored embeddings in ChromaDB for: {original_filename}")
                        else:
                            self.logger.warning(f"Failed to store embeddings in ChromaDB for: {original_filename}")
                    except Exception as e:
                        self.logger.error(f"ChromaDB processing error for {original_filename}: {e}")

                self.logger.info(f"Successfully processed and stored: {original_filename}")
                return True
            else:
                self.logger.error(f"Failed to store {original_filename} in MongoDB")
                return False

        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def process_single_file_sync(self, file_path: str, temp_dir: str) -> bool:
        """
        Synchronous wrapper for process_single_file to use with ThreadPoolExecutor.

        Args:
            file_path: Path to the file to process
            temp_dir: Temporary directory for intermediate files

        Returns:
            True if processing was successful, False otherwise
        """
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.process_single_file(file_path, temp_dir))
        finally:
            loop.close()

    def get_processable_files(self, folder_path: str) -> List[str]:
        """
        Get list of processable files from the folder.

        Args:
            folder_path: Path to the folder containing files

        Returns:
            List of file paths that can be processed
        """
        processable_files = []
        supported_extensions = SUPPORTED_IMAGE_FORMATS + SUPPORTED_DOC_FORMATS + [PDF_FORMAT]

        try:
            if not os.path.exists(folder_path):
                self.logger.error(f"Folder does not exist: {folder_path}")
                return processable_files

            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)

                # Skip directories
                if os.path.isdir(file_path):
                    continue

                # Check if file extension is supported
                file_ext = Path(file_name).suffix.lower()
                if file_ext in supported_extensions:
                    processable_files.append(file_path)
                    self.logger.info(f"Found processable file: {file_name}")
                else:
                    self.logger.warning(f"Unsupported file format, skipping: {file_name}")

            self.logger.info(f"Found {len(processable_files)} processable files in {folder_path}")
            return processable_files

        except Exception as e:
            self.logger.error(f"Error scanning folder {folder_path}: {e}")
            return processable_files

    async def process_folder(self, folder_path: str) -> Dict[str, Any]:
        """
        Process all files in the specified folder using parallel processing.

        Args:
            folder_path: Path to the folder containing files to process

        Returns:
            Dictionary containing processing results and statistics
        """
        start_time = datetime.now()
        results = {
            "start_time": start_time,
            "folder_path": folder_path,
            "total_files": 0,
            "processed_successfully": 0,
            "failed_files": 0,
            "duplicate_files": 0,
            "processed_files": [],
            "failed_files_list": [],
            "duplicate_files_list": [],
            "n_workers": self.n_workers
        }

        try:
            self.logger.info(f"Starting parallel folder processing: {folder_path}")
            self.logger.info(f"Using {self.n_workers} parallel workers")

            # Get list of processable files
            files_to_process = self.get_processable_files(folder_path)
            results["total_files"] = len(files_to_process)

            if not files_to_process:
                self.logger.warning("No processable files found in the folder")
                return results

            # Pre-filter duplicates to avoid unnecessary processing
            self.logger.info("Pre-filtering duplicate files...")
            non_duplicate_files = []
            for file_path in files_to_process:
                file_name = os.path.basename(file_path)
                try:
                    checksum = self.calculate_checksum(file_path)
                    if self.is_duplicate_thread_safe(checksum):
                        results["duplicate_files"] += 1
                        results["duplicate_files_list"].append(file_name)
                        self.logger.info(f"Duplicate detected, skipping: {file_name}")
                    else:
                        non_duplicate_files.append(file_path)
                except Exception as e:
                    self.logger.error(f"Error checking duplicate for {file_name}: {e}")
                    results["failed_files"] += 1
                    results["failed_files_list"].append(file_name)

            self.logger.info(f"Found {len(non_duplicate_files)} non-duplicate files to process")

            if not non_duplicate_files:
                self.logger.info("No new files to process after duplicate filtering")
                return results

            # Create temporary directory for intermediate files
            with tempfile.TemporaryDirectory() as temp_dir:
                self.logger.info(f"Using temporary directory: {temp_dir}")

                # Process files in parallel using ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=self.n_workers) as executor:
                    self.logger.info(f"Starting parallel processing with {self.n_workers} workers...")

                    # Submit all tasks
                    future_to_file = {
                        executor.submit(self.process_single_file_sync, file_path, temp_dir): file_path
                        for file_path in non_duplicate_files
                    }

                    # Process completed tasks
                    completed = 0
                    for future in as_completed(future_to_file):
                        file_path = future_to_file[future]
                        file_name = os.path.basename(file_path)
                        completed += 1

                        try:
                            success = future.result()

                            if success:
                                results["processed_successfully"] += 1
                                results["processed_files"].append(file_name)
                                self.logger.info(f"[{completed}/{len(non_duplicate_files)}] ✓ {file_name}")
                            else:
                                results["failed_files"] += 1
                                results["failed_files_list"].append(file_name)
                                self.logger.error(f"[{completed}/{len(non_duplicate_files)}] ✗ {file_name}")

                        except Exception as e:
                            self.logger.error(f"[{completed}/{len(non_duplicate_files)}] Exception processing {file_name}: {e}")
                            results["failed_files"] += 1
                            results["failed_files_list"].append(file_name)

            # Calculate processing time
            end_time = datetime.now()
            results["end_time"] = end_time
            results["processing_time"] = str(end_time - start_time)

            # Log summary
            self.logger.info("=" * 60)
            self.logger.info("PARALLEL PROCESSING SUMMARY")
            self.logger.info("=" * 60)
            self.logger.info(f"Folder: {folder_path}")
            self.logger.info(f"Workers: {self.n_workers}")
            self.logger.info(f"Total files found: {results['total_files']}")
            self.logger.info(f"Successfully processed: {results['processed_successfully']}")
            self.logger.info(f"Failed: {results['failed_files']}")
            self.logger.info(f"Duplicates skipped: {results['duplicate_files']}")
            self.logger.info(f"Processing time: {results['processing_time']}")

            # Calculate throughput
            if results['processed_successfully'] > 0:
                total_seconds = (end_time - start_time).total_seconds()
                throughput = results['processed_successfully'] / total_seconds if total_seconds > 0 else 0
                self.logger.info(f"Throughput: {throughput:.2f} files/second")

            self.logger.info("=" * 60)

            return results

        except Exception as e:
            self.logger.error(f"Error processing folder {folder_path}: {e}")
            self.logger.error(traceback.format_exc())
            results["error"] = str(e)
            return results


# Utility functions for easy usage
async def process_documents_from_folder(folder_path: str,
                                      database_name: str = "dbProductionV2",
                                      collection_name: str = "collectionResumeV2_chroma",
                                      n_workers: int = 12,
                                      enable_chromadb: bool = True,
                                      chroma_host: str = "localhost",
                                      chroma_port: int = 8001) -> Dict[str, Any]:
    """
    Convenience function to process all documents in a folder with parallel processing.

    Args:
        folder_path: Path to the folder containing documents
        database_name: MongoDB database name (default: "dbProductionV2")
        collection_name: MongoDB collection name (default: "collectionResumeV2_chroma")
        n_workers: Number of parallel workers (default: 12)
        enable_chromadb: Whether to enable ChromaDB integration (default: True)
        chroma_host: ChromaDB server host (default: "localhost")
        chroma_port: ChromaDB server port (default: 8001)

    Returns:
        Dictionary containing processing results and statistics
    """
    processor = DocumentProcessor(
        database_name,
        collection_name,
        n_workers=n_workers,
        enable_chromadb=enable_chromadb,
        chroma_host=chroma_host,
        chroma_port=chroma_port
    )
    return await processor.process_folder(folder_path)


def get_file_from_gridfs(database_name: str, file_id: ObjectId, output_path: str) -> bool:
    """
    Retrieve a file from GridFS and save it to the specified path.

    Args:
        database_name: MongoDB database name
        file_id: GridFS file ID
        output_path: Path where to save the retrieved file

    Returns:
        True if successful, False otherwise
    """
    try:
        mongo_client = MongoDBClient(db_name=database_name)
        fs = gridfs.GridFS(mongo_client.db)

        # Get the file from GridFS
        grid_out = fs.get(file_id)

        # Write to output path
        with open(output_path, 'wb') as output_file:
            output_file.write(grid_out.read())

        print(f"File retrieved successfully: {output_path}")
        return True

    except Exception as e:
        print(f"Error retrieving file from GridFS: {e}")
        return False


def list_processed_documents(database_name: str = "dbProductionV2",
                           collection_name: str = "collectionResumeV2_chroma") -> List[Dict[str, Any]]:
    """
    List all processed documents in the collection.

    Args:
        database_name: MongoDB database name
        collection_name: MongoDB collection name

    Returns:
        List of documents with basic information
    """
    try:
        mongo_client = MongoDBClient(db_name=database_name)
        collection = mongo_client.db[collection_name]

        # Get all documents with basic fields
        documents = list(collection.find(
            {},
            {
                "_id": 1,
                "original_filename": 1,
                "timestamp": 1,
                "checksum": 1,
                "pdf_file_id": 1,
                "vendor_name": 1
            }
        ))

        return documents

    except Exception as e:
        print(f"Error listing documents: {e}")
        return []


# Main execution
if __name__ == "__main__":
    import argparse
    import sys

    def main():
        """Main function for command-line usage."""
        parser = argparse.ArgumentParser(
            description="Process documents from a folder and store in MongoDB",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  # Process all files in a folder with default settings (12 workers)
  python mongoDBInsertion.py /path/to/documents

  # Process with custom database, collection, and worker count
  python mongoDBInsertion.py /path/to/documents --database my-db --collection my-collection --workers 8

  # Process with maximum parallelism (adjust based on your system)
  python mongoDBInsertion.py /path/to/documents --workers 20

  # List all processed documents
  python mongoDBInsertion.py --list

  # Retrieve a file from GridFS
  python mongoDBInsertion.py --get-file 507f1f77bcf86cd799439011 --output retrieved.pdf
            """
        )

        parser.add_argument(
            "folder_path",
            default=r"\\************\user_data\PAVAN\Desktop\Gemini\resumes",
            help="Path to the folder containing documents to process"
        )

        parser.add_argument(
            "--database",
            default="dbProductionV2",
            help="MongoDB database name (default: dbProductionV2)"
        )

        parser.add_argument(
            "--collection",
            default="collectionResumeV2_chroma",
            help="MongoDB collection name (default: collectionResumeV2_chroma)"
        )

        parser.add_argument(
            "--workers",
            type=int,
            default=12,
            help="Number of parallel workers for processing (default: 12)"
        )

        parser.add_argument(
            "--list",
            action="store_true",
            help="List all processed documents"
        )

        parser.add_argument(
            "--get-file",
            help="Retrieve a file from GridFS by ObjectId"
        )

        parser.add_argument(
            "--output",
            help="Output path for retrieved file (used with --get-file)"
        )

        args = parser.parse_args()

        # Handle list command
        if args.list:
            print("Listing processed documents...")
            documents = list_processed_documents(args.database, args.collection)

            if documents:
                print(f"\nFound {len(documents)} processed documents:")
                print("-" * 80)
                for doc in documents:
                    print(f"ID: {doc['_id']}")
                    print(f"Filename: {doc.get('original_filename', 'N/A')}")
                    print(f"Processed: {doc.get('timestamp', 'N/A')}")
                    print(f"PDF File ID: {doc.get('pdf_file_id', 'N/A')}")
                    print("-" * 80)
            else:
                print("No processed documents found.")
            return

        # Handle get-file command
        if args.get_file:
            if not args.output:
                print("Error: --output is required when using --get-file")
                sys.exit(1)

            try:
                file_id = ObjectId(args.get_file)
                success = get_file_from_gridfs(args.database, file_id, args.output)
                if success:
                    print(f"File retrieved successfully: {args.output}")
                else:
                    print("Failed to retrieve file")
                    sys.exit(1)
            except Exception as e:
                print(f"Error: Invalid ObjectId or retrieval failed: {e}")
                sys.exit(1)
            return

        # Handle folder processing
        if not args.folder_path:
            print("Error: folder_path is required for processing")
            parser.print_help()
            sys.exit(1)

        if not os.path.exists(args.folder_path):
            print(f"Error: Folder does not exist: {args.folder_path}")
            sys.exit(1)

        # Process the folder
        async def run_processing():
            print(f"Processing documents from: {args.folder_path}")
            print(f"Database: {args.database}")
            print(f"Collection: {args.collection}")
            print(f"Parallel workers: {args.workers}")
            print("-" * 50)

            results = await process_documents_from_folder(
                args.folder_path,
                args.database,
                args.collection,
                args.workers
            )

            # Print results
            print("\nProcessing completed!")
            print(f"Total files: {results.get('total_files', 0)}")
            print(f"Successfully processed: {results.get('processed_successfully', 0)}")
            print(f"Failed: {results.get('failed_files', 0)}")
            print(f"Duplicates skipped: {results.get('duplicate_files', 0)}")
            print(f"Processing time: {results.get('processing_time', 'N/A')}")

            if results.get('failed_files_list'):
                print(f"\nFailed files: {', '.join(results['failed_files_list'])}")

            if results.get('duplicate_files_list'):
                print(f"\nDuplicate files: {', '.join(results['duplicate_files_list'])}")

        # Run the async processing
        try:
            asyncio.run(run_processing())
        except KeyboardInterrupt:
            print("\nProcessing interrupted by user")
            sys.exit(1)
        except Exception as e:
            print(f"Error during processing: {e}")
            sys.exit(1)

    main()
