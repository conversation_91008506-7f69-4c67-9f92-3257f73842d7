# AWS Textract Timing Fix Summary

## Problem Description

You were experiencing this error:
```
ERROR - Error extracting text from C:\Users\<USER>\AppData\Local\Temp\tmpvy2ud1yq\CV_Ashvini_Pal_martial arts instructor .pdf: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpvy2ud1yq\\CV_Ashvini_Pal_martial arts instructor \\CV_Ashvini_Pal_martial arts instructor _ExtractedText.txt'
```

## Root Cause Analysis

The issue was caused by a **race condition** between:
1. **AWS Textract processing**: Takes time to extract text and save files
2. **File reading logic**: Immediately tries to read files that don't exist yet

Additional complications:
- **Filename with spaces**: `CV_Ashvini_Pal_martial arts instructor .pdf`
- **Inconsistent file naming**: AWS Textract creates files with specific patterns
- **Asynchronous processing**: Multiple files being processed simultaneously

## Solution Implemented

### 1. **Dynamic File Detection**
```python
def _find_textract_output_files(self, pdf_folder: str, pdf_name: str = None) -> List[str]:
    """Dynamically find all text files created by AWS Textract"""
```

**Features:**
- Scans the output folder for any `.txt` or `.json` files
- Prioritizes files with expected patterns (`_ExtractedText`, `_Text`, etc.)
- Handles files with any naming convention
- Checks file sizes to ensure content exists

### 2. **Exponential Backoff Waiting**
```python
max_wait_time = 120  # Maximum wait time in seconds
wait_interval = 1    # Initial wait interval
total_waited = 0

while total_waited < max_wait_time:
    # Check for files
    if text_file_exists:
        break
    
    # Wait with exponential backoff
    await asyncio.sleep(wait_interval)
    total_waited += wait_interval
    wait_interval = min(wait_interval * 1.5, 10)  # Cap at 10 seconds
```

**Benefits:**
- Waits intelligently for AWS Textract to complete
- Starts with short waits (1s) and increases gradually
- Maximum wait time of 120 seconds with timeout protection
- Caps wait intervals at 10 seconds to avoid excessive delays

### 3. **Multiple Retry Attempts**
```python
max_read_attempts = 5
for attempt in range(max_read_attempts):
    # Refresh file list on each attempt
    if attempt > 0:
        dynamic_files = self._find_textract_output_files(pdf_folder, pdf_name)
        all_possible_files = dynamic_files + [f for f in possible_text_files if f not in dynamic_files]
    
    # Try to read files
    for text_file_path in all_possible_files:
        # ... reading logic
```

**Features:**
- Up to 5 attempts to read text files
- Refreshes file list on each attempt (dynamic discovery)
- Exponential backoff between attempts (1s, 2s, 4s, 8s, 16s)
- Continues processing even if individual files fail

### 4. **Multi-Format Support**
```python
if text_file_path.endswith('.json'):
    # Handle JSON format from AWS Textract
    with open(text_file_path, 'r', encoding='utf-8') as file:
        json_data = json.load(file)
        content = self._extract_text_from_json(json_data)
else:
    # Handle text files
    with open(text_file_path, 'r', encoding='utf-8') as file:
        content = file.read().strip()
```

**Supports:**
- `.txt` files (CSV format from AWS Textract)
- `.json` files (structured data from AWS Textract)
- Automatic format detection and parsing
- Fallback to string representation if parsing fails

### 5. **Enhanced Error Logging**
```python
# Check file sizes to see if they're still being written
for file_name in files_in_folder:
    file_path = os.path.join(pdf_folder, file_name)
    if os.path.isfile(file_path):
        file_size = os.path.getsize(file_path)
        self.logger.error(f"  - {file_name}: {file_size} bytes")
```

**Provides:**
- Detailed folder contents listing
- File size information for debugging
- Clear error messages with context
- Debug information for troubleshooting

## Files Modified

### `mongoDBInsertion.py`
- **`extract_text_from_pdf()`**: Enhanced with timing fixes
- **`_extract_text_from_json()`**: New method for JSON parsing
- **`_find_textract_output_files()`**: New dynamic file detection

## Testing

### Test Script: `test_textract_timing_fix.py`
```bash
python test_textract_timing_fix.py
```

**Tests:**
1. **File Detection Logic**: Tests dynamic file finding without AWS
2. **AWS Textract Timing**: Tests actual AWS Textract with timing fixes
3. **Filename Handling**: Tests files with spaces and special characters

## Expected Results

### Before Fix:
```
ERROR - Error extracting text from ...: [Errno 2] No such file or directory
```

### After Fix:
```
INFO - Starting AWS Textract extraction for: CV_Ashvini_Pal_martial arts instructor .pdf
INFO - AWS Textract extraction completed for: CV_Ashvini_Pal_martial arts instructor .pdf
INFO - Text files detected after 3s wait
INFO - Found text files in folder: ['CV_Ashvini_Pal_martial arts instructor _ExtractedText.txt']
INFO - Successfully read text from: .../CV_Ashvini_Pal_martial arts instructor _ExtractedText.txt
```

## Performance Impact

### Timing Improvements:
- **Reduced Failures**: Eliminates race condition errors
- **Smart Waiting**: Only waits when necessary
- **Parallel Safe**: Works correctly with multiple workers
- **Resource Efficient**: Exponential backoff prevents excessive polling

### Error Recovery:
- **Automatic Retry**: Recovers from temporary file access issues
- **Graceful Degradation**: Continues processing other files if one fails
- **Detailed Logging**: Provides clear information for debugging

## Configuration

### Timing Parameters (adjustable):
```python
max_wait_time = 120      # Maximum wait for AWS Textract (seconds)
max_read_attempts = 5    # Maximum file read attempts
wait_interval = 1        # Initial wait interval (seconds)
```

### File Detection Patterns:
- `_ExtractedText.txt` (primary AWS Textract output)
- `_Text.json` (JSON format from AWS Textract)
- `_Combined.txt` (legacy format)
- `_strUserPrompt.txt` (alternative format)

## Monitoring

### Log Messages to Watch:
- `"Starting AWS Textract extraction"` - Process begins
- `"Text files detected after Xs wait"` - Files found successfully
- `"Successfully read text from"` - Content extracted successfully
- `"Timeout waiting for text extraction files"` - Potential AWS issues

### Performance Metrics:
- **Wait Time**: How long it takes for AWS Textract to complete
- **Retry Count**: Number of attempts needed to read files
- **Success Rate**: Percentage of successful text extractions

## Troubleshooting

### If Still Getting Errors:

1. **Check AWS Credentials**: Ensure AWS Textract access is configured
2. **Verify S3 Bucket**: Confirm bucket permissions and accessibility
3. **Monitor Disk Space**: Ensure sufficient space for temporary files
4. **Check File Permissions**: Verify write access to temp directories
5. **AWS Service Status**: Check if AWS Textract service is operational

### Debug Commands:
```python
# Enable debug logging
import logging
logging.getLogger().setLevel(logging.DEBUG)

# Test file detection manually
processor = DocumentProcessor()
found_files = processor._find_textract_output_files("/path/to/folder")
print(f"Found files: {found_files}")
```

## Summary

The timing fix provides:
✅ **Robust file detection** - Finds files regardless of naming conventions
✅ **Smart waiting logic** - Waits appropriately for AWS Textract completion  
✅ **Multiple retry attempts** - Recovers from temporary failures
✅ **Multi-format support** - Handles both text and JSON outputs
✅ **Enhanced error logging** - Provides detailed debugging information
✅ **Parallel processing safe** - Works correctly with multiple workers

This should completely resolve the `[Errno 2] No such file or directory` errors you were experiencing!
